// 测试字段映射功能
// 直接定义映射函数来测试逻辑
const fieldNameMapping = {
  "ls172": "陵水17-2",
  "ls251": "陵水25-1",
  "yc131": "崖城13-1",
  "yc1310": "崖城13-10"
};

const organizationMapping = {
  '香港中电': 'hk_power',
  '气电南山': 'nanshan_gas',
  '气电广东': 'guangdong_gas'
};

function getChineseFieldName(fieldKey) {
  return fieldNameMapping[fieldKey] || fieldKey;
}

function getOrganizationProp(orgName) {
  return organizationMapping[orgName] || orgName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_').toLowerCase();
}

function getSupportedFields() {
  return Object.keys(fieldNameMapping);
}

function hasFieldMapping(fieldKey) {
  return fieldKey in fieldNameMapping;
}

// 测试数据
const sampleData = {
  code: 200,
  data: {
    result: [
      {
        yc131: 2.55,
        ls172: 12.7,
        name: "香港中电",
        yc1310: 0.0
      },
      {
        ls172: 3.82,
        name: "气电南山"
      },
      {
        ls251: 0.55,
        ls172: 15.49,
        name: "气电广东"
      }
    ],
    success: true,
    maxQ: 35.492
  },
  msg: "查询成功"
};

// 格式化数值函数
function formatNumber(value) {
  if (value === null || value === undefined || value === '') {
    return '0.00';
  }
  const num = Number(value);
  if (isNaN(num)) {
    return '0.00';
  }
  return num.toFixed(2);
}

// 获取所有键
function getAllKeys(data) {
  const keys = new Set();
  data.forEach(item => {
    Object.keys(item).forEach(key => {
      if (key !== 'name') {
        keys.add(key);
      }
    });
  });
  return Array.from(keys).sort();
}

// 处理表格数据（带字段映射）
function getProcessedTableDataWithMapping(data) {
  const allKeys = getAllKeys(data);
  const tableData = [];

  allKeys.forEach(key => {
    const row = {
      rowHeader: getChineseFieldName(key), // 使用中文字段名
      rowTotal: 0
    };

    data.forEach(item => {
      const propName = getOrganizationProp(item.name);
      const value = item[key] || 0;
      row[propName] = formatNumber(value);
      row.rowTotal += Number(value);
    });

    row.rowTotal = formatNumber(row.rowTotal);
    tableData.push(row);
  });

  return tableData;
}

// 测试字段映射功能
console.log('=== 字段映射测试 ===');
console.log('支持的字段:', getSupportedFields());
console.log('');

console.log('字段名映射测试:');
console.log('ls172 ->', getChineseFieldName('ls172'));
console.log('ls251 ->', getChineseFieldName('ls251'));
console.log('yc131 ->', getChineseFieldName('yc131'));
console.log('yc1310 ->', getChineseFieldName('yc1310'));
console.log('unknown_field ->', getChineseFieldName('unknown_field'));
console.log('');

console.log('机构名映射测试:');
console.log('香港中电 ->', getOrganizationProp('香港中电'));
console.log('气电南山 ->', getOrganizationProp('气电南山'));
console.log('气电广东 ->', getOrganizationProp('气电广东'));
console.log('');

console.log('字段映射检查:');
console.log('hasFieldMapping("ls172"):', hasFieldMapping('ls172'));
console.log('hasFieldMapping("unknown"):', hasFieldMapping('unknown'));
console.log('');

// 测试完整的表格数据处理
const data = sampleData.data.result;
console.log('=== 带字段映射的表格数据 ===');
console.log(JSON.stringify(getProcessedTableDataWithMapping(data), null, 2));

console.log('\n=== 预期效果 ===');
console.log('行标题应该显示中文字段名:');
console.log('- 陵水17-2 (而不是 ls172)');
console.log('- 陵水25-1 (而不是 ls251)');
console.log('- 崖城13-1 (而不是 yc131)');
console.log('- 崖城13-10 (而不是 yc1310)');
