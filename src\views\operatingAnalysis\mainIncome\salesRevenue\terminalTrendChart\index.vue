<template>
  <div class="terminal-trend-chart">
    <div class="chart-container">
      <!-- 左侧卡片区域 -->
      <div class="card-section">
        <div class="terminal-card" v-for="terminal in terminals" :key="terminal.name">
          <div class="terminal-icon">
            <div class="icon-placeholder"></div>
          </div>
          <div class="terminal-info">
            <div class="terminal-name">{{ terminal.name }}</div>
            <div class="terminal-value">
              <span class="value">{{ terminal.value }}</span>
              <span class="unit">{{ terminal.unit }}</span>
            </div>
            <div class="terminal-change">
              <span class="label">同比</span>
              <span class="change-value" :class="terminal.changeType">
                {{ terminal.change }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧图表区域 -->
      <div class="chart-section">
        <div class="chart-box" ref="chartBox"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "TerminalTrendChart",
  props: {},
  data() {
    return {
      myChart: null,
      // 终端数据
      terminals: [
        {
          name: "天然气",
          value: "30",
          unit: "亿方",
          change: "↑ 0.12亿方 12%",
          changeType: "positive"
        },
        {
          name: "网山",
          value: "25",
          unit: "亿方",
          change: "↑ 0.08亿方 8%",
          changeType: "positive"
        },
        {
          name: "香港",
          value: "20",
          unit: "亿方",
          change: "↓ 0.05亿方 5%",
          changeType: "negative"
        }
      ],
      // 图表数据
      chartData: {
        dates: ["1/1", "1/2", "1/4", "1/8", "1/16", "1/24", "1/32"],
        terminals: [
          {
            name: "终端销售",
            planned: [20, 35, 45, 20, 35, 45, 40],
            actual: [25, 40, 30, 25, 40, 50, 45]
          },
          {
            name: "网山",
            planned: [30, 45, 50, 30, 45, 50, 45],
            actual: [35, 50, 35, 35, 50, 55, 50]
          },
          {
            name: "香港",
            planned: [25, 40, 45, 25, 40, 45, 40],
            actual: [30, 45, 30, 30, 45, 50, 45]
          }
        ]
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(this.$refs.chartBox);
      
      const option = {
        backgroundColor: "transparent",
        tooltip: {
          trigger: "axis",
          confine: true,
          borderWidth: 0,
          backgroundColor: "rgba(12, 15, 41, 0.9)",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);border-radius: 8px;",
          textStyle: {
            fontFamily: "Source Han Sans",
            color: "#FFFFFF",
            fontSize: 12,
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.2)',
              width: 1,
              type: 'solid'
            }
          }
        },
        legend: {
          data: ["计划", "实际"],
          textStyle: {
            color: "#ACC2E2",
            fontSize: 12,
          },
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          right: "5%",
          top: "2%",
        },
        grid: [
          {
            top: "8%",
            left: "8%",
            right: "8%",
            height: "25%",
            containLabel: true,
          },
          {
            top: "38%",
            left: "8%",
            right: "8%",
            height: "25%",
            containLabel: true,
          },
          {
            top: "68%",
            left: "8%",
            right: "8%",
            height: "25%",
            containLabel: true,
          }
        ],
        xAxis: [
          {
            gridIndex: 0,
            type: "category",
            data: this.chartData.dates,
            axisTick: { show: false },
            axisLine: {
              show: true,
              lineStyle: { color: "rgba(172, 194, 226, 0.2)" }
            },
            axisLabel: {
              textStyle: { color: "#ACC2E2", fontSize: 10 },
              interval: 0
            }
          },
          {
            gridIndex: 1,
            type: "category",
            data: this.chartData.dates,
            axisTick: { show: false },
            axisLine: {
              show: true,
              lineStyle: { color: "rgba(172, 194, 226, 0.2)" }
            },
            axisLabel: {
              textStyle: { color: "#ACC2E2", fontSize: 10 },
              interval: 0
            }
          },
          {
            gridIndex: 2,
            type: "category",
            data: this.chartData.dates,
            axisTick: { show: false },
            axisLine: {
              show: true,
              lineStyle: { color: "rgba(172, 194, 226, 0.2)" }
            },
            axisLabel: {
              textStyle: { color: "#ACC2E2", fontSize: 10 },
              interval: 0
            }
          }
        ],
        yAxis: [
          {
            gridIndex: 0,
            type: "value",
            name: "高栏",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "left",
              padding: [0, 0, 0, 10]
            },
            axisLabel: {
              textStyle: { color: "#ACC2E2", fontSize: 10 }
            },
            splitLine: { show: false },
            axisLine: { show: false }
          },
          {
            gridIndex: 1,
            type: "value",
            name: "网山",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "left",
              padding: [0, 0, 0, 10]
            },
            axisLabel: {
              textStyle: { color: "#ACC2E2", fontSize: 10 }
            },
            splitLine: { show: false },
            axisLine: { show: false }
          },
          {
            gridIndex: 2,
            type: "value",
            name: "香港",
            nameTextStyle: {
              color: "#ACC2E2",
              align: "left",
              padding: [0, 0, 0, 10]
            },
            axisLabel: {
              textStyle: { color: "#ACC2E2", fontSize: 10 }
            },
            splitLine: { show: false },
            axisLine: { show: false }
          }
        ],
        series: this.generateSeries()
      };

      this.myChart.setOption(option);
    },

    generateSeries() {
      const series = [];
      
      this.chartData.terminals.forEach((terminal, index) => {
        // 计划线
        series.push({
          name: "计划",
          type: "line",
          xAxisIndex: index,
          yAxisIndex: index,
          data: terminal.planned,
          lineStyle: {
            color: "#1783FF",
            width: 2
          },
          itemStyle: {
            color: "#1783FF"
          },
          symbol: "circle",
          symbolSize: 4,
          smooth: true
        });
        
        // 实际线
        series.push({
          name: "实际",
          type: "line",
          xAxisIndex: index,
          yAxisIndex: index,
          data: terminal.actual,
          lineStyle: {
            color: "#FF6B6B",
            width: 2
          },
          itemStyle: {
            color: "#FF6B6B"
          },
          symbol: "circle",
          symbolSize: 4,
          smooth: true
        });
      });
      
      return series;
    }
  }
};
</script>

<style lang="scss" scoped>
.terminal-trend-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 12px 16px;
  flex: 1;
  min-height: 0;

  .chart-container {
    display: flex;
    height: 100%;
    gap: 16px;
    flex: 1;
    min-height: 0;

    .card-section {
      width: 180px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 4px 0;

      .terminal-card {
        background: #1a2e5e;
        border: 1px solid rgba(23, 131, 255, 0.3);
        border-radius: 6px;
        padding: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        min-height: 60px;

        .terminal-icon {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(23, 131, 255, 0.1);
          border-radius: 50%;
          flex-shrink: 0;

          .icon-placeholder {
            width: 16px;
            height: 16px;
            background: #1783FF;
            border-radius: 3px;
          }
        }

        .terminal-info {
          flex: 1;
          min-width: 0;

          .terminal-name {
            color: #ACC2E2;
            font-size: 11px;
            margin-bottom: 2px;
            font-family: Source Han Sans;
          }

          .terminal-value {
            display: flex;
            align-items: baseline;
            margin-bottom: 2px;

            .value {
              color: #FFFFFF;
              font-size: 16px;
              font-weight: bold;
              font-family: D-DIN;
            }

            .unit {
              color: #ACC2E2;
              font-size: 10px;
              margin-left: 2px;
              font-family: Source Han Sans;
            }
          }

          .terminal-change {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 9px;

            .label {
              color: #ACC2E2;
              font-family: Source Han Sans;
            }

            .change-value {
              font-family: Source Han Sans;

              &.positive {
                color: #00FF88;
              }

              &.negative {
                color: #FF6B6B;
              }
            }
          }
        }
      }
    }

    .chart-section {
      flex: 1;
      min-width: 0;

      .chart-box {
        width: 100%;
        height: 100%;
        min-height: 300px;
      }
    }
  }
}
</style>
